import datetime

from pydantic import BaseModel


class PropertyAvailableTimeslots(BaseModel):
    property_available_times: dict[str, dict[str, str]]
    searched_date_from: datetime.date
    searched_date_to: datetime.date
    timeslots_note: str = (
        "Available timeslots are for searched_date_from and searched_date_to date range, "
        "ALWAYS call this tool again to search for a different date and time"
    )
    self_schedule_note: str


class Appointment(BaseModel):
    appointment_id: int
    appointment_status: str
    appointment_start_time: datetime.datetime
