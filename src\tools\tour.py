import time
from datetime import UTC, datetime
from uuid import uuid4

from fastapi import APIRouter, HTTPException
from fastmcp import FastMC<PERSON>
from pydantic import HttpUrl

from src.models.cancel_tour import Appointment, CancelTourResponse, Phone, Profile, Prospect
from src.models.schedule_tour import ProfilePayload, QueuedRequest, ScheduleTourRequest, ScheduleTourResponse
from src.models.tour import AvailableTimes, PropertyAvailableTimesResponse, ScheduleTourLink
from src.utils.log_config import get_logger


class TourValidation:
    INVALID_APPOINTMENT_ID = "Invalid appointment ID: must be positive"
    # Test appointment ID that simulates a not found error
    TEST_NOT_FOUND_APPOINTMENT_ID = 909999


logger = get_logger(__name__)
router = APIRouter()


def _validate_property_id(property_id: int) -> None:
    """Validate that property_id is provided."""
    if not property_id:
        raise ValueError("Property ID is required")  # noqa: TRY003


async def _get_property_available_times(property_id: int) -> PropertyAvailableTimesResponse:
    """
    Get the available times for a property

    Args:
        property_id: The ID of the property

    Returns:
        A PropertyAvailableTimesResponse containing available times
    """
    logger.info(f"Getting available times for property {property_id}")
    try:
        _validate_property_id(property_id)

        return PropertyAvailableTimesResponse(
            available_times=AvailableTimes(
                acceptable_times=[
                    "2025-07-10T07:00:00-05:00",
                    "2025-07-10T07:30:00-05:00",
                    "2025-07-10T08:00:00-05:00",
                ],
                allow_non_preferred_times="",
                preferred_times=[],
                preferred_times_instant_book="",
                reviewable_times=[],
            )
        )
    except ValueError:
        return PropertyAvailableTimesResponse(
            available_times=AvailableTimes(
                acceptable_times=[],
                allow_non_preferred_times="",
                preferred_times=[],
                preferred_times_instant_book="",
                reviewable_times=[],
            )
        )


def retrieve_schedule_tour_link(prospect_id: int) -> ScheduleTourLink:
    """
    Generate a link to schedule a tour.

    Returns:
        str: A URL to schedule a tour.
    """
    logger.info("Generating schedule tour link for prospect ID: %s", prospect_id)
    logger.debug("Prospect ID: %s", prospect_id)
    return ScheduleTourLink(url=HttpUrl("http://alpha-shorturl.knocktest.com/WblkdQ"))


def post_schedule_tour(request: ScheduleTourRequest) -> ScheduleTourResponse:
    """
    Mock implementation for scheduling a tour.

    Args:
        request: The tour scheduling request

    Returns:
        A mock response for the scheduled tour
    """
    logger.info("Processing tour request for property ID: %s", request.appointment_info.property_id)

    # Create a mock queued request
    queued_request = QueuedRequest(
        created_time=datetime.now(UTC),
        id=int(time.time()),  # Use timestamp as mock ID
        is_deleted=False,
        listing_ids=request.appointment_info.listing_ids,
        modified_time=None,
        profile_payload=ProfilePayload(
            email=request.appointment_info.profile.email,
            first_name=request.appointment_info.profile.first_name,
            last_name=request.appointment_info.profile.last_name,
            pets=request.appointment_info.profile.pets,
            phone_number=request.appointment_info.profile.phone_number,
            reference_info=None,
            target_move_date=request.appointment_info.profile.target_move_date,
        ),
        property_id=request.appointment_info.property_id,
        pusher_channel=f"queued-request-{uuid4().hex[:16]}",  # Generate a random channel ID
        renter_id=int(time.time()) + 1000,  # Mock renter ID
        requested_times=[time.start_time for time in request.appointment_info.requested_times],
        status="queued",
        unit_id=request.appointment_info.unit_id,
    )

    return ScheduleTourResponse(queued_request=queued_request, status_code="existing_queued_request")


def post_cancel_tour(appointment_id: int) -> CancelTourResponse:
    """
    Mock implementation for canceling a tour.

    Args:
        appointment_id: The ID of the appointment to cancel

    Returns:
        A mock response for the canceled tour

    Raises:
        ValueError: If appointment_id is invalid
        HTTPException: If appointment is not found
    """
    logger.info("Processing tour cancellation for appointment ID: %s", appointment_id)

    # Validate appointment ID
    if appointment_id <= 0:
        raise ValueError(TourValidation.INVALID_APPOINTMENT_ID)

    # Simulate not found error for specific ID
    if appointment_id == TourValidation.TEST_NOT_FOUND_APPOINTMENT_ID:
        raise HTTPException(status_code=404, detail="Appointment not found")

    # Create mock phone data
    phone = Phone(
        caller_id_name="Invalid Phone",
        caller_id_type="invalid",
        can_receive_call=True,
        can_receive_sms=True,
        carrier_name="",
        carrier_type="",
        country_code="US",
        created_time=datetime(2023, 3, 9, 23, 27, 10, 975225, tzinfo=UTC),
        id=136597,
        is_deleted=False,
        modified_time=datetime(2024, 9, 30, 8, 24, 40, 157996, tzinfo=UTC),
        national_format="(*************",
        phone_number="+***********",
    )

    # Create mock profile data
    profile = Profile(
        address_id=None,
        bio=None,
        co_tenants=None,
        created_by_type="manager",
        created_time=datetime(2023, 9, 18, 12, 49, 46, 669613, tzinfo=UTC),
        email="<EMAIL>",
        first_name="Adr",
        formatted_phone_number="(*************",
        id=90436,
        id_verified=None,
        id_verify_report_url=None,
        income=None,
        is_criminal=None,
        is_deleted=False,
        is_winback_enabled=True,
        last_name="Test",
        modified_time=datetime(2024, 9, 16, 10, 47, 13, 457424, tzinfo=UTC),
        pets=None,
        phone=phone,
        phone_id=136597,
        phone_number="+***********",
        photo="https://alpha-knockphotos.s3.amazonaws.com/common/assets/profile_placeholder.png",
        selfiescan_pdf_key=None,
        target_move_date="2024-09-19",
        verification_method=None,
        was_evicted=None,
    )

    # Create mock prospect data
    prospect = Prospect(
        ai_email_enabled=True,
        assigned_manager_id=240712,
        assigned_relay_phone="+***********",
        business_time_to_first_response=None,
        created_time=datetime(2023, 9, 18, 12, 49, 46, 742999, tzinfo=UTC),
        creation_source="doorway-form",
        developer_id=None,
        first_contact_type="form",
        first_response_time=datetime(2024, 2, 29, 13, 56, 19, 726688, tzinfo=UTC),
        has_call_recording=None,
        has_note=True,
        id=68482,
        is_active=True,
        is_deleted=False,
        is_excluded=False,
        is_waitlist=False,
        last_contacted_time=datetime.now(UTC),
        last_relevant_time=datetime.now(UTC),
        last_response_time=None,
        leased_date=None,
        modified_time=datetime.now(UTC),
        origin="knock",
        pms_created_time=None,
        preferences_id=100201,
        profile=profile,
        profile_id=90436,
        property_id=21534,
        renter_id=246727,
        resource_id=103751,
        sms_consent_id=61129,
        source="Property Website",
        status="cancelled-renter",
        stream_id="YLKW251P-1695041387",
        time_to_first_response=None,
    )

    # Create mock appointment data
    appointment = Appointment(
        auto_accepted=True,
        created_time=datetime(2024, 2, 29, 14, 10, 40, 149122, tzinfo=UTC),
        developer_id=None,
        end_time=datetime(2024, 2, 29, 16, 0, 0, tzinfo=UTC),
        id=appointment_id,
        is_deleted=False,
        manager_id=240712,
        modified_time=datetime(2024, 5, 9, 7, 50, 54, 11821, tzinfo=UTC),
        origin="knock",
        prospect=prospect,
        prospect_id=68482,
        reference_info=None,
        reminder_id=None,
        request_id=None,
        resource_id=389358,
        review_reminder_id=None,
        review_token=None,
        source="Property Website",
        start_time=datetime(2024, 2, 29, 15, 30, 0, tzinfo=UTC),
        status="cancelled",
        stream_id="YLKW251P-1695041387",
        tour_type=None,
        type="request",
        uuid=str(uuid4()),
    )

    return CancelTourResponse(appointment=appointment, status_code="ok")


@router.post(
    "/cancel_tour/{appointment_id}",
    operation_id="post_cancel_tour",
    response_model=CancelTourResponse,
    description="Cancel a scheduled tour appointment.",
)
async def cancel_tour(appointment_id: int) -> CancelTourResponse:
    """Cancel a scheduled tour appointment."""
    logger.info("Received cancel tour request for appointment ID: %s", appointment_id)
    try:
        response = post_cancel_tour(appointment_id)
        logger.info("Successfully processed tour cancellation")
    except Exception:
        logger.exception("Failed to process tour cancellation")
        raise
    else:
        return response


def register_tour_tools(mcp: FastMCP) -> None:
    """
    Register the tour tools with the MCP server.

    Args:
        mcp: The MCP server instance to register tools with
    """

    @mcp.tool(
        name="get_schedule_tour_link",
        description="Get a link to schedule a tour for a prospect.",
    )
    def get_schedule_tour_link(prospect_id: int) -> ScheduleTourLink:
        time.sleep(1)  # Simulate processing delay
        return retrieve_schedule_tour_link(prospect_id)

    """
    Get the available times for a property
    """

    @mcp.tool(
        name="get_property_available_times",
        description="Get the available times for a property",
    )
    async def get_property_available_times(property_id: int) -> PropertyAvailableTimesResponse:
        return await _get_property_available_times(property_id)

    @mcp.tool(
        name="schedule_tour",
        description="Schedule a tour for the given property and time slots.",
    )
    def schedule_tour(request: ScheduleTourRequest) -> ScheduleTourResponse:
        time.sleep(1)  # Simulate processing delay
        return post_schedule_tour(request)

    @mcp.tool(
        name="cancel_tour",
        description="Cancel a scheduled tour appointment.",
    )
    def cancel_tour(appointment_id: int) -> CancelTourResponse:
        time.sleep(1)  # Simulate processing delay
        return post_cancel_tour(appointment_id)


@router.post(
    "/schedule_tour",
    operation_id="post_schedule_tour",
    response_model=ScheduleTourResponse,
    description="Schedule a tour for the given prospect in a given property and time slots.",
)
async def schedule_tour(request: ScheduleTourRequest) -> ScheduleTourResponse:
    return post_schedule_tour(request)
