# Update Prospect Consent Guide

This document provides a comprehensive guide to the `update_prospect_consent` tool available in the Knock MCP Server.

## Overview

The `update_prospect_consent` tool allows you to update the SMS consent status of a prospect. This is a critical function for compliance with SMS marketing regulations and managing prospect communication preferences.

## Tool Details

- **Tool Name**: `update_prospect_consent`
- **Description**: Update the consent status of a prospect
- **Return Type**: `dict` containing the updated prospect information

## Parameters

### Required Parameters

1. **`prospect_id`** (int)
   - Unique identifier of the prospect
   - Must be a positive integer
   - Example: `12345`

2. **`consent_info`** (SmsConsent)
   - Object containing the consent information to update
   - Type: `SmsConsent` model

## SmsConsent Model Structure

The `SmsConsent` model contains the following fields:

```python
class SmsConsent(BaseModel):
    bypass_consent: bool                    # Whether to bypass consent requirements
    created_time: datetime                  # When the consent record was created
    has_consent: bool                       # Whether the prospect has given consent
    has_express_consent_override: bool      # Whether express consent override is enabled
    id: int                                 # Unique identifier for the consent record
    is_deleted: bool                        # Whether the consent record is deleted
    modified_time: datetime                 # When the consent record was last modified
    note: str | None = None                 # Optional note about the consent
    status: str                             # Current consent status
```

## Valid Consent Status Values

The following consent status values are supported:

| Status | Description | Has Consent | Default Note |
|--------|-------------|-------------|--------------|
| `granted` | Prospect has granted SMS consent | `true` | `None` |
| `declined` | Prospect has declined SMS consent | `false` | "User declined SMS consent" |
| `revoked` | Prospect has revoked previously granted consent | `false` | "User revoked SMS consent" |
| `new` | New consent request (no decision yet) | `false` | "New consent request" |

## Usage Examples

### Example 1: Granting SMS Consent

```python
from datetime import datetime
from src.models.prospect import SmsConsent

# Create consent info for granting SMS consent
consent_info = SmsConsent(
    bypass_consent=False,
    created_time=datetime.now(),
    has_consent=True,
    has_express_consent_override=False,
    id=12345,
    is_deleted=False,
    modified_time=datetime.now(),
    note="Prospect explicitly granted SMS consent",
    status="granted"
)

# Update the prospect's consent
result = update_prospect_consent(
    prospect_id=12345,
    consent_info=consent_info
)

print(f"Updated prospect {result['prospect']['id']}")
print(f"Consent status: {result['prospect']['sms_consent']['status']}")
```

### Example 2: Declining SMS Consent

```python
from datetime import datetime
from src.models.prospect import SmsConsent

# Create consent info for declining SMS consent
consent_info = SmsConsent(
    bypass_consent=False,
    created_time=datetime.now(),
    has_consent=False,
    has_express_consent_override=False,
    id=12345,
    is_deleted=False,
    modified_time=datetime.now(),
    note="Prospect declined SMS consent during signup",
    status="declined"
)

# Update the prospect's consent
result = update_prospect_consent(
    prospect_id=12345,
    consent_info=consent_info
)
```

### Example 3: Revoking Previously Granted Consent

```python
from datetime import datetime
from src.models.prospect import SmsConsent

# Create consent info for revoking consent
consent_info = SmsConsent(
    bypass_consent=False,
    created_time=datetime.now(),
    has_consent=False,
    has_express_consent_override=False,
    id=12345,
    is_deleted=False,
    modified_time=datetime.now(),
    note="Prospect revoked consent via unsubscribe link",
    status="revoked"
)

# Update the prospect's consent
result = update_prospect_consent(
    prospect_id=12345,
    consent_info=consent_info
)
```

## Response Format

The function returns a dictionary with the following structure:

```python
{
    "prospect": {
        "id": int,                    # Prospect ID
        "sms_consent": SmsConsent,    # Updated SMS consent object
        "modified_time": str          # ISO format timestamp of the update
    }
}
```

### Example Response

```python
{
    "prospect": {
        "id": 12345,
        "sms_consent": {
            "bypass_consent": False,
            "created_time": "2024-01-15T14:30:25",
            "has_consent": True,
            "has_express_consent_override": False,
            "id": 89611,
            "is_deleted": False,
            "modified_time": "2024-01-15T14:30:25",
            "note": "Prospect explicitly granted SMS consent",
            "status": "granted"
        },
        "modified_time": "2024-01-15T14:30:25"
    }
}
```

## Error Handling

### Invalid Consent Status

If an invalid consent status is provided, the function will raise a `ValueError`:

```python
try:
    result = update_prospect_consent(
        prospect_id=12345,
        consent_info=invalid_consent_info
    )
except ValueError as e:
    print(f"Error: {e}")
    # Output: "Error: Invalid consent status: invalid_status"
```

### Common Error Scenarios

1. **Invalid prospect_id**: Ensure the prospect ID is a valid positive integer
2. **Invalid consent status**: Use only the supported status values: `granted`, `declined`, `revoked`, `new`
3. **Missing required fields**: Ensure all required fields in the `SmsConsent` model are provided

## Best Practices

### 1. Consent Status Management

- Always use the predefined consent status values
- Provide meaningful notes for audit trails
- Update `modified_time` when making changes
- Ensure `has_consent` matches the status value

### 2. Data Validation

- Validate prospect ID before calling the function
- Ensure consent info contains all required fields
- Use appropriate consent status for the scenario

### 3. Audit Trail

- Always include descriptive notes for consent changes
- Maintain accurate timestamps for all modifications
- Log consent changes for compliance purposes

### 4. Compliance Considerations

- Respect prospect's consent preferences
- Maintain records of consent changes
- Follow applicable SMS marketing regulations
- Document the reason for consent changes

## Integration with MCP

This tool is automatically converted to an MCP tool by the FastApiMCP framework, making it available for use in AI-powered workflows. The tool provides:

- Type-safe parameter validation
- Automatic documentation generation
- Integration with MCP-compatible clients
- Structured response format

## Mock Implementation Notes

The current implementation uses mock data for demonstration purposes. In a production environment, you would need to:

1. Replace mock data with actual database operations
2. Add proper authentication and authorization
3. Implement data validation and sanitization
4. Add audit logging for all consent changes
5. Implement proper error handling and status codes
6. Add rate limiting and security measures
7. Ensure compliance with SMS marketing regulations (TCPA, GDPR, etc.)

## Related Tools

- `get_prospect_guestcard`: Retrieve prospect information including current consent status
- `update_prospect_guestcard`: Update other prospect information
- `update_prospect_status`: Update prospect status

## Support

For questions or issues related to this tool, please refer to the main project documentation or contact the development team.
