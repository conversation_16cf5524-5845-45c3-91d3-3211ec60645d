from unittest.mock import Mock

import pytest

from src.tools.tour import (
    AvailableTimes,
    PropertyAvailableTimesResponse,
    _get_property_available_times,
)


class TestGetPropertyAvailableTimes:
    """Test cases for _get_property_available_times function"""

    @pytest.mark.asyncio
    async def test_get_property_available_times_success(self) -> None:
        """Test successful retrieval of property available times"""
        property_id = 123
        result = await _get_property_available_times(property_id)

        assert isinstance(result, PropertyAvailableTimesResponse)

        assert isinstance(result.available_times, AvailableTimes)
        assert len(result.available_times.acceptable_times) == 3
        assert "2025-07-10T07:00:00-05:00" in result.available_times.acceptable_times
        assert "2025-07-10T07:30:00-05:00" in result.available_times.acceptable_times
        assert "2025-07-10T08:00:00-05:00" in result.available_times.acceptable_times

    @pytest.mark.asyncio
    async def test_get_property_available_times_with_zero_id(self) -> None:
        """Test that zero property ID returns error response"""
        result = await _get_property_available_times(0)

        assert isinstance(result, PropertyAvailableTimesResponse)

        assert result.available_times.acceptable_times == []

    @pytest.mark.asyncio
    async def test_get_property_available_times_with_empty_string_id(self) -> None:
        """Test that empty string property ID returns error response"""
        result = await _get_property_available_times("")

        assert isinstance(result, PropertyAvailableTimesResponse)

        assert result.available_times.acceptable_times == []

    @pytest.mark.asyncio
    async def test_get_property_available_times_with_negative_id(self) -> None:
        """Test that negative property ID works (since it's truthy)"""
        property_id = -1
        result = await _get_property_available_times(property_id)

        assert isinstance(result, PropertyAvailableTimesResponse)

    @pytest.mark.asyncio
    async def test_get_property_available_times_response_structure(self) -> None:
        """Test the structure of the response object"""
        property_id = 456
        result = await _get_property_available_times(property_id)

        # Test AvailableTimes structure
        available_times = result.available_times
        assert isinstance(available_times.acceptable_times, list)
        assert isinstance(available_times.allow_non_preferred_times, str)
        assert isinstance(available_times.preferred_times, list)
        assert isinstance(available_times.preferred_times_instant_book, str)
        assert isinstance(available_times.reviewable_times, list)

        # Test that non-preferred times fields are empty strings
        assert available_times.allow_non_preferred_times == ""
        assert available_times.preferred_times_instant_book == ""

        # Test that preferred_times and reviewable_times are empty lists
        assert available_times.preferred_times == []
        assert available_times.reviewable_times == []

    @pytest.mark.asyncio
    async def test_get_property_available_times_with_large_id(self) -> None:
        """Test with a very large property ID"""
        property_id = 999999999
        result = await _get_property_available_times(property_id)

        assert isinstance(result, PropertyAvailableTimesResponse)

        assert len(result.available_times.acceptable_times) > 0
