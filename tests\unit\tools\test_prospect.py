from fastapi.testclient import TestClient

from main import app
from src.tools.prospect import (
    GuestCardPreferences,
    GuestCardProfile,
    GuestCardRequestType,
    ProspectActivityResponse,
    ProspectStatusResponse,
    update_prospect_guestcard,
    update_prospect_consent_tool,
)
from src.models.prospect import SmsConsent
from src.config.config import ConsentStatus
from datetime import datetime

client = TestClient(app)


class TestGuestCardMCPTools:
    """Test cases for guest card MCP tools."""

    def test_update_prospect_guestcard_function(self) -> None:
        """Test the update_prospect_guestcard function directly."""
        prospect_id = 122123
        profile = GuestCardProfile(
            first_name="<PERSON>",
            last_name="<PERSON>",
            target_move_date="2024-07-01",
            email="<EMAIL>",
            phone={"phone_number": "******-456-7890"},
        )
        preferences = GuestCardPreferences(bedrooms=["2", "3"])

        result = update_prospect_guestcard(prospect_id, profile, preferences)

        assert result.profile.first_name == "<PERSON>"
        assert result.profile.last_name == "<PERSON>"
        assert result.profile.email == "<EMAIL>"
        assert result.profile.phone.phone_number == "******-456-7890"
        assert result.preferences.bedrooms == ["2", "3"]

    def test_update_prospect_guestcard_minimal_profile(self) -> None:
        """Test updating a guest card with minimal required profile data."""
        prospect_id = 122124
        profile = GuestCardProfile(
            first_name="Bob",
            last_name="Smith",
        )

        result = update_prospect_guestcard(prospect_id, profile)

        assert result.profile.first_name == "Bob"
        assert result.profile.last_name == "Smith"
        assert result.profile.target_move_date is None
        assert result.profile.email is None
        assert result.profile.phone is None
        assert result.preferences is None

    def test_update_prospect_guestcard_without_preferences(self) -> None:
        """Test updating a guest card without preferences."""
        prospect_id = 122125
        profile = GuestCardProfile(
            first_name="Carol",
            last_name="Davis",
            target_move_date="2024-08-15",
            email="<EMAIL>",
        )

        result = update_prospect_guestcard(prospect_id, profile)

        assert result.profile.first_name == "Carol"
        assert result.profile.last_name == "Davis"
        assert result.profile.target_move_date == "2024-08-15"
        assert result.profile.email == "<EMAIL>"
        assert result.preferences is None


class TestProspectStatus:
    """Test cases for prospect status operations."""

    def test_update_prospect_status_success(self) -> None:
        """Test successfully updating prospect status."""
        prospect_id = 122129
        status = "qualified"
        response = client.put(f"/prospect/{prospect_id}/status?status={status}")
        assert response.status_code == 200
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["status"] == status

    def test_update_prospect_status_invalid_id(self) -> None:
        """Test updating prospect status with invalid ID."""
        status = "qualified"
        response = client.put(f"/prospect/invalid_id/status?status={status}")
        assert response.status_code == 422  # FastAPI validation error

    def test_update_prospect_status_empty_status(self) -> None:
        """Test updating prospect status with empty status."""
        prospect_id = 122130
        response = client.put(f"/prospect/{prospect_id}/status?status=")
        assert response.status_code == 200  # Empty string is valid
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["status"] == ""

    def test_update_prospect_status_special_characters(self) -> None:
        """Test updating prospect status with special characters."""
        prospect_id = 122131
        status = "in-progress (pending)"
        response = client.put(f"/prospect/{prospect_id}/status?status={status}")
        assert response.status_code == 200
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["status"] == status

    def test_update_prospect_status_missing_status_parameter(self) -> None:
        """Test updating prospect status without status parameter."""
        prospect_id = 122132
        response = client.put(f"/prospect/{prospect_id}/status")
        assert response.status_code == 422  # FastAPI validation error - missing required parameter


class TestProspectActivity:
    """Test cases for prospect activity operations."""

    def test_add_prospect_activity_success(self) -> None:
        """Test successfully adding activity to prospect."""
        prospect_id = 122133
        activity = "Viewed property listing"
        response = client.post(f"/prospect/{prospect_id}/activity?activity={activity}")
        assert response.status_code == 200
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["activity"] == activity

    def test_add_prospect_activity_invalid_id(self) -> None:
        """Test adding activity with invalid prospect ID."""
        activity = "Viewed property listing"
        response = client.post(f"/prospect/invalid_id/activity?activity={activity}")
        assert response.status_code == 422  # FastAPI validation error

    def test_add_prospect_activity_empty_activity(self) -> None:
        """Test adding empty activity to prospect."""
        prospect_id = 122134
        response = client.post(f"/prospect/{prospect_id}/activity?activity=")
        assert response.status_code == 200  # Empty string is valid
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["activity"] == ""

    def test_add_prospect_activity_long_text(self) -> None:
        """Test adding long activity text to prospect."""
        prospect_id = 122135
        activity = "This is a very long activity description that might contain detailed information about what the prospect did during their visit to the property, including their reactions and any questions they asked."
        response = client.post(f"/prospect/{prospect_id}/activity?activity={activity}")
        assert response.status_code == 200
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["activity"] == activity

    def test_add_prospect_activity_special_characters(self) -> None:
        """Test adding activity with special characters."""
        prospect_id = 122136
        activity = "Contacted via email: <EMAIL> (urgent)"
        response = client.post(f"/prospect/{prospect_id}/activity?activity={activity}")
        assert response.status_code == 200
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["activity"] == activity

    def test_add_prospect_activity_missing_activity_parameter(self) -> None:
        """Test adding activity without activity parameter."""
        prospect_id = 122137
        response = client.post(f"/prospect/{prospect_id}/activity")
        assert response.status_code == 422  # FastAPI validation error - missing required parameter


class TestGuestCardModels:
    """Test cases for Pydantic models used in guest card operations."""

    def test_guest_card_profile_model(self) -> None:
        """Test GuestCardProfile model validation."""
        # Test valid profile
        profile = GuestCardProfile(
            first_name="John",
            last_name="Doe",
            target_move_date="2024-09-01",
            email="<EMAIL>",
            phone={"phone_number": "******-123-4567"},
        )
        assert profile.first_name == "John"
        assert profile.last_name == "Doe"
        assert profile.target_move_date == "2024-09-01"
        assert profile.email == "<EMAIL>"
        assert profile.phone.phone_number == "******-123-4567"

    def test_guest_card_profile_model_minimal(self) -> None:
        """Test GuestCardProfile model with minimal required fields."""
        profile = GuestCardProfile(first_name="Jane", last_name="Smith")
        assert profile.first_name == "Jane"
        assert profile.last_name == "Smith"
        assert profile.target_move_date is None
        assert profile.email is None
        assert profile.phone is None

    def test_guest_card_preferences_model(self) -> None:
        """Test GuestCardPreferences model validation."""
        preferences = GuestCardPreferences(bedrooms=["1", "2", "3"])
        assert preferences.bedrooms == ["1", "2", "3"]

    def test_guest_card_preferences_model_empty(self) -> None:
        """Test GuestCardPreferences model with empty bedrooms."""
        preferences = GuestCardPreferences(bedrooms=[])
        assert preferences.bedrooms == []

    def test_guest_card_request_type_model(self) -> None:
        """Test GuestCardRequestType model validation."""
        request = GuestCardRequestType(
            profile=GuestCardProfile(first_name="Jane", last_name="Smith"),
            preferences=GuestCardPreferences(bedrooms=["2"]),
        )
        assert request.profile.first_name == "Jane"
        assert request.profile.last_name == "Smith"
        assert request.preferences.bedrooms == ["2"]

    def test_guest_card_request_type_model_without_preferences(self) -> None:
        """Test GuestCardRequestType model without preferences."""
        request = GuestCardRequestType(profile=GuestCardProfile(first_name="Jane", last_name="Smith"))
        assert request.profile.first_name == "Jane"
        assert request.profile.last_name == "Smith"
        assert request.preferences is None

    def test_prospect_status_response_model(self) -> None:
        """Test ProspectStatusResponse model validation."""
        response = ProspectStatusResponse(prospect_id=12345, status="active")
        assert response.prospect_id == 12345
        assert response.status == "active"

    def test_prospect_activity_response_model(self) -> None:
        """Test ProspectActivityResponse model validation."""
        response = ProspectActivityResponse(prospect_id=12345, activity="Scheduled tour")
        assert response.prospect_id == 12345
        assert response.activity == "Scheduled tour"

    def test_guest_card_phone_model(self) -> None:
        """Test GuestCardPhone model validation."""
        phone = {"phone_number": "******-123-4567"}
        assert phone["phone_number"] == "******-123-4567"

    def test_guest_card_phone_model_empty(self) -> None:
        """Test GuestCardPhone model with empty phone number."""
        phone = {"phone_number": None}
        assert phone["phone_number"] is None


class TestUpdateProspectConsent:
    """Test cases for update_prospect_consent_tool function."""

    def test_update_prospect_consent_tool_granted(self) -> None:
        """Test updating prospect consent to granted status."""
        prospect_id = 12345
        consent_info = SmsConsent(
            bypass_consent=False,
            created_time=datetime.now(),
            has_consent=True,
            has_express_consent_override=False,
            id=1,
            is_deleted=False,
            modified_time=datetime.now(),
            note=None,
            status="granted",
        )

        result = update_prospect_consent_tool(prospect_id, consent_info)

        assert result["prospect"]["id"] == prospect_id
        assert result["prospect"]["sms_consent"]["has_consent"] is True
        assert result["prospect"]["sms_consent"]["status"] == "granted"
        assert result["prospect"]["sms_consent"]["note"] is None
        assert result["prospect"]["sms_consent"]["bypass_consent"] is False
        assert result["prospect"]["sms_consent"]["has_express_consent_override"] is False
        assert result["prospect"]["sms_consent"]["is_deleted"] is False
        assert "created_time" in result["prospect"]["sms_consent"]
        assert "modified_time" in result["prospect"]["sms_consent"]
        assert "id" in result["prospect"]["sms_consent"]

    def test_update_prospect_consent_tool_declined(self) -> None:
        """Test updating prospect consent to declined status."""
        prospect_id = 12346
        consent_info = SmsConsent(
            bypass_consent=False,
            created_time=datetime.now(),
            has_consent=False,
            has_express_consent_override=False,
            id=2,
            is_deleted=False,
            modified_time=datetime.now(),
            note="User declined SMS consent",
            status="declined",
        )

        result = update_prospect_consent_tool(prospect_id, consent_info)

        assert result["prospect"]["id"] == prospect_id
        assert result["prospect"]["sms_consent"]["has_consent"] is False
        assert result["prospect"]["sms_consent"]["status"] == "declined"
        assert result["prospect"]["sms_consent"]["note"] == "User declined SMS consent"

    def test_update_prospect_consent_tool_revoked(self) -> None:
        """Test updating prospect consent to revoked status."""
        prospect_id = 12347
        consent_info = SmsConsent(
            bypass_consent=False,
            created_time=datetime.now(),
            has_consent=False,
            has_express_consent_override=False,
            id=3,
            is_deleted=False,
            modified_time=datetime.now(),
            note="User revoked SMS consent",
            status="revoked",
        )

        result = update_prospect_consent_tool(prospect_id, consent_info)

        assert result["prospect"]["id"] == prospect_id
        assert result["prospect"]["sms_consent"]["has_consent"] is False
        assert result["prospect"]["sms_consent"]["status"] == "revoked"
        assert result["prospect"]["sms_consent"]["note"] == "User revoked SMS consent"

    def test_update_prospect_consent_tool_new(self) -> None:
        """Test updating prospect consent to new status."""
        prospect_id = 12348
        consent_info = SmsConsent(
            bypass_consent=False,
            created_time=datetime.now(),
            has_consent=False,
            has_express_consent_override=False,
            id=4,
            is_deleted=False,
            modified_time=datetime.now(),
            note="New consent request",
            status="new",
        )

        result = update_prospect_consent_tool(prospect_id, consent_info)

        assert result["prospect"]["id"] == prospect_id
        assert result["prospect"]["sms_consent"]["has_consent"] is False
        assert result["prospect"]["sms_consent"]["status"] == "new"
        assert result["prospect"]["sms_consent"]["note"] == "New consent request"

    def test_update_prospect_consent_tool_with_bypass(self) -> None:
        """Test updating prospect consent with bypass consent enabled."""
        prospect_id = 12349
        consent_info = SmsConsent(
            bypass_consent=True,
            created_time=datetime.now(),
            has_consent=True,
            has_express_consent_override=True,
            id=5,
            is_deleted=False,
            modified_time=datetime.now(),
            note="Bypass consent for testing",
            status="granted",
        )

        result = update_prospect_consent_tool(prospect_id, consent_info)

        assert result["prospect"]["id"] == prospect_id
        assert result["prospect"]["sms_consent"]["bypass_consent"] is True
        assert result["prospect"]["sms_consent"]["has_express_consent_override"] is True
        assert result["prospect"]["sms_consent"]["note"] == "Bypass consent for testing"

    def test_update_prospect_consent_tool_with_custom_note(self) -> None:
        """Test updating prospect consent with a custom note."""
        prospect_id = 12350
        custom_note = "Custom note for testing purposes"
        consent_info = SmsConsent(
            bypass_consent=False,
            created_time=datetime.now(),
            has_consent=True,
            has_express_consent_override=False,
            id=6,
            is_deleted=False,
            modified_time=datetime.now(),
            note=custom_note,
            status="granted",
        )

        result = update_prospect_consent_tool(prospect_id, consent_info)

        assert result["prospect"]["id"] == prospect_id
        assert result["prospect"]["sms_consent"]["note"] == custom_note

    def test_update_prospect_consent_tool_timestamp_consistency(self) -> None:
        """Test that created_time and modified_time are consistent in the response."""
        prospect_id = 12351
        current_time = datetime.now()
        consent_info = SmsConsent(
            bypass_consent=False,
            created_time=current_time,
            has_consent=True,
            has_express_consent_override=False,
            id=7,
            is_deleted=False,
            modified_time=current_time,
            note=None,
            status="granted",
        )

        result = update_prospect_consent_tool(prospect_id, consent_info)

        # Check that timestamps are present and in ISO format
        assert "created_time" in result["prospect"]["sms_consent"]
        assert "modified_time" in result["prospect"]["sms_consent"]
        assert "modified_time" in result["prospect"]

        # Verify timestamp format (should be ISO format without microseconds)
        created_time = result["prospect"]["sms_consent"]["created_time"]
        modified_time = result["prospect"]["sms_consent"]["modified_time"]
        prospect_modified_time = result["prospect"]["modified_time"]

        # All timestamps should be the same (rounded to second)
        assert created_time == modified_time
        assert modified_time == prospect_modified_time

    def test_update_prospect_consent_tool_unique_id_generation(self) -> None:
        """Test that unique SMS consent IDs are generated correctly."""
        prospect_id_1 = 12352
        prospect_id_2 = 12353

        consent_info = SmsConsent(
            bypass_consent=False,
            created_time=datetime.now(),
            has_consent=True,
            has_express_consent_override=False,
            id=8,
            is_deleted=False,
            modified_time=datetime.now(),
            note=None,
            status="granted",
        )

        result_1 = update_prospect_consent_tool(prospect_id_1, consent_info)
        result_2 = update_prospect_consent_tool(prospect_id_2, consent_info)

        # IDs should be different and follow the pattern 89611 + prospect_id % 1000
        expected_id_1 = 89611 + prospect_id_1 % 1000
        expected_id_2 = 89611 + prospect_id_2 % 1000

        assert result_1["prospect"]["sms_consent"]["id"] == expected_id_1
        assert result_2["prospect"]["sms_consent"]["id"] == expected_id_2
        assert result_1["prospect"]["sms_consent"]["id"] != result_2["prospect"]["sms_consent"]["id"]

    def test_update_prospect_consent_tool_response_structure(self) -> None:
        """Test that the response structure is correct and complete."""
        prospect_id = 12354
        consent_info = SmsConsent(
            bypass_consent=False,
            created_time=datetime.now(),
            has_consent=True,
            has_express_consent_override=False,
            id=9,
            is_deleted=False,
            modified_time=datetime.now(),
            note="Test note",
            status="granted",
        )

        result = update_prospect_consent_tool(prospect_id, consent_info)

        # Check top-level structure
        assert "prospect" in result

        # Check prospect structure
        prospect = result["prospect"]
        assert "id" in prospect
        assert "sms_consent" in prospect
        assert "modified_time" in prospect

        # Check sms_consent structure
        sms_consent = prospect["sms_consent"]
        required_fields = [
            "bypass_consent",
            "created_time",
            "has_consent",
            "has_express_consent_override",
            "id",
            "is_deleted",
            "modified_time",
            "note",
            "status",
        ]
        for field in required_fields:
            assert field in sms_consent
