from datetime import datetime
from typing import ClassVar

from pydantic import BaseModel, EmailStr, Field, model_validator


class Pets(BaseModel):
    cats: bool
    large_dogs: bool
    none: bool
    small_dogs: bool


# Input Schema Models
class RequestedTime(BaseModel):
    start_time: str = Field(
        default="",
        pattern=r"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:?\d{2}$",
        description="Start time in ISO format with timezone",
    )


class MarketingSource(BaseModel):
    code: str = Field(min_length=1)
    domain: str
    doorway_application_id: str = Field(default="", min_length=1)


class Preferences(BaseModel):
    min_price: int = Field(default=0, ge=0)
    max_price: int = Field(default=0, ge=0)
    bedrooms: list[int] = Field(default=[])
    selected_times: list[str] = Field(default=[], min_length=1)

    PRICE_VALIDATION_ERROR: ClassVar[str] = "max_price ({}) must be greater than or equal to min_price ({})"

    @model_validator(mode="after")
    def validate_prices(self) -> "Preferences":
        """Validate that max_price is greater than or equal to min_price."""
        if self.max_price < self.min_price:
            raise ValueError(self.PRICE_VALIDATION_ERROR.format(self.max_price, self.min_price))
        return self


class Profile(BaseModel):
    first_name: str = Field(min_length=1)
    last_name: str = Field(min_length=1)
    email: EmailStr
    phone_number: str = Field(default="", pattern=r"^\(\d{3}\) \d{3}-\d{4}$")
    target_move_date: str = Field(default="", pattern=r"^\d{4}-\d{2}-\d{2}$")
    pets: Pets


class AppointmentInfo(BaseModel):
    property_id: int = Field(default=1, gt=0)
    requested_times: list[RequestedTime] = Field(default=[], min_length=1)
    type: str = Field(default="tour", pattern="^tour$")
    profile: Profile
    listing_ids: list[int] = Field(default=[])
    unit_id: int | None = None
    marketing_source: MarketingSource
    tour_type: str | None = None
    preferences: Preferences


class ScheduleTourRequest(BaseModel):
    appointment_info: AppointmentInfo


# Response Schema Models
class ProfilePayload(BaseModel):
    email: EmailStr
    first_name: str = Field(default="Test", min_length=1)
    last_name: str = Field(default="Test", min_length=1)
    pets: Pets
    phone_number: str = Field(default="", pattern=r"^\(\d{3}\) \d{3}-\d{4}$")
    reference_info: str | None = None
    target_move_date: str = Field(default="", pattern=r"^\d{4}-\d{2}-\d{2}$")


class QueuedRequest(BaseModel):
    created_time: datetime
    id: int = Field(default=1, gt=0)
    is_deleted: bool
    listing_ids: list[int] = Field(default=[])
    modified_time: datetime | None = None
    profile_payload: ProfilePayload
    property_id: int = Field(default=1, gt=0)
    pusher_channel: str = Field(default="queued-request-", min_length=1)
    renter_id: int = Field(default=1, gt=0)
    requested_times: list[str] = Field(default=[], min_length=1)
    status: str = Field(default="queued", pattern="^queued$")
    unit_id: int | None = None


class ScheduleTourResponse(BaseModel):
    queued_request: QueuedRequest
    status_code: str = Field(default="existing_queued_request", pattern="^existing_queued_request$")
