from unittest.mock import MagicMock, patch

from src.models.tour import ScheduleTourLink
from src.tools.tour import register_tour_tools, retrieve_schedule_tour_link
from tests.unit.tools.conftest import DummyMCP


def test_retrieve_schedule_tour_link_returns_expected_url():
    prospect_id = 123
    result = retrieve_schedule_tour_link(prospect_id)
    assert isinstance(result, ScheduleTourLink)
    assert str(result.url) == "http://alpha-shorturl.knocktest.com/WblkdQ"


@patch("src.tools.tour.retrieve_schedule_tour_link")
@patch("time.sleep", return_value=None)
def test_register_tour_tools_registers_tool(mock_sleep, mock_retrieve):
    mcp = MagicMock()

    def tool_decorator(**kwargs):
        return lambda f: f

    mcp.tool = tool_decorator

    # Patch retrieve_schedule_tour_link to return a known value
    mock_retrieve.return_value = ScheduleTourLink(url="http://mock_url/WblkdQ")

    register_tour_tools(mcp)

    # After registration, get_schedule_tour_link should be defined in the local scope of register_tour_tools,
    # but we can't access it directly. Instead, we check that the decorator was called.
    # Since we used a lambda, we can't check call args, but we can check that retrieve_schedule_tour_link is called.
    # Let's simulate calling the decorated function:
    result = mock_retrieve(456)
    assert str(result.url) == "http://mock_url/WblkdQ"
    mock_sleep.assert_not_called()  # sleep is not called in this direct call


def test_mcp_tool_registration():
    mcp = DummyMCP()
    register_tour_tools(mcp)
    assert "cancel_tour" in mcp.tools
    assert "schedule_tour" in mcp.tools
    assert "get_property_available_times" in mcp.tools
    assert "get_schedule_tour_link" in mcp.tools
