"""Unit tests for schedule tour functionality."""

from datetime import datetime, timezone
from unittest.mock import MagicMock, patch, call

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient
from pydantic import ValidationError

from src.models.schedule_tour import (
    AppointmentInfo,
    MarketingSource,
    Pets,
    Preferences,
    Profile,
    ScheduleTourRequest,
    ScheduleTourResponse,
)
from src.tools.tour import post_schedule_tour, register_tour_tools, router

# Create a test app and include the router
app = FastAPI()
app.include_router(router, prefix="/tour")
client = TestClient(app)


@pytest.fixture
def valid_request() -> ScheduleTourRequest:
    """Fixture for creating a valid tour request."""
    return ScheduleTourRequest(
        appointment_info=AppointmentInfo(
            property_id=21521,
            requested_times=[{"start_time": "2025-02-06T11:00:00-06:00"}],
            type="tour",
            profile=Profile(
                first_name="<PERSON>",
                last_name="<PERSON><PERSON>",
                email="<EMAIL>",
                phone_number="(*************",
                target_move_date="2025-03-01",
                pets=Pets(none=True, large_dogs=False, small_dogs=False, cats=False),
            ),
            listing_ids=[],
            unit_id=None,
            marketing_source=MarketingSource(code="w", domain="", doorway_application_id="abc123"),
            preferences=Preferences(
                min_price=1000, max_price=2000, bedrooms=[1, 2], selected_times=["2025-02-06T11:00:00-06:00"]
            ),
        )
    )


def test_schedule_tour_success(valid_request):
    """Test successful tour scheduling."""
    response = post_schedule_tour(valid_request)

    assert isinstance(response, ScheduleTourResponse)
    assert response.status_code == "existing_queued_request"

    # Verify queued request details
    queued = response.queued_request
    assert queued.property_id == valid_request.appointment_info.property_id
    assert queued.listing_ids == valid_request.appointment_info.listing_ids
    assert queued.unit_id == valid_request.appointment_info.unit_id
    assert queued.status == "queued"
    assert not queued.is_deleted

    # Verify profile payload
    profile = queued.profile_payload
    assert profile.first_name == valid_request.appointment_info.profile.first_name
    assert profile.last_name == valid_request.appointment_info.profile.last_name
    assert profile.email == valid_request.appointment_info.profile.email
    assert profile.phone_number == valid_request.appointment_info.profile.phone_number
    assert profile.target_move_date == valid_request.appointment_info.profile.target_move_date
    assert profile.pets == valid_request.appointment_info.profile.pets


def test_schedule_tour_validation():
    """Test input validation for schedule tour."""
    # Test with invalid data
    invalid_data = {
        "appointment_info": {
            "property_id": 0,  # Invalid: must be > 0
            "requested_times": [],  # Invalid: must have at least one item
            "type": "invalid",  # Invalid: must be "tour"
            "profile": {
                "first_name": "",  # Invalid: must not be empty
                "last_name": "",  # Invalid: must not be empty
                "email": "invalid-email",  # Invalid: must be valid email
                "phone_number": "************",  # Invalid: must match (XXX) XXX-XXXX
                "target_move_date": "2025/03/01",  # Invalid: must be YYYY-MM-DD
                "pets": {"none": True, "large_dogs": False, "small_dogs": False, "cats": False},
            },
            "listing_ids": [],  # Valid: can be empty
            "unit_id": None,  # Valid: can be None
            "marketing_source": {
                "code": "",  # Invalid: must not be empty
                "domain": "",  # Valid: can be empty
                "doorway_application_id": "",  # Invalid: must not be empty
            },
            "preferences": {
                "min_price": -1,  # Invalid: must be >= 0
                "max_price": -2,  # Invalid: must be >= 0
                "bedrooms": [],  # Valid: can be empty
                "selected_times": [],  # Invalid: must have at least one item
            },
        }
    }

    with pytest.raises(ValidationError) as exc_info:
        ScheduleTourRequest(**invalid_data)

    errors = exc_info.value.errors()

    # Create a map of error paths to their types
    error_map = {}
    for error in errors:
        path = tuple(error["loc"])
        error_map[path] = error

    # Helper function to check error details
    def assert_error(path, type, **ctx):
        assert path in error_map, f"Missing error for {path}"
        error = error_map[path]
        assert error["type"] == type, f"Wrong error type for {path}. Expected {type}, got {error['type']}"
        if ctx:
            for key, value in ctx.items():
                assert error.get("ctx", {}).get(key) == value, f"Wrong {key} for {path}"

    # Check each validation error
    assert_error(("appointment_info", "property_id"), "greater_than", gt=0)
    assert_error(("appointment_info", "requested_times"), "too_short", min_length=1)
    assert_error(("appointment_info", "type"), "string_pattern_mismatch", pattern="^tour$")
    assert_error(("appointment_info", "profile", "first_name"), "string_too_short", min_length=1)
    assert_error(("appointment_info", "profile", "last_name"), "string_too_short", min_length=1)
    assert_error(("appointment_info", "profile", "email"), "value_error")
    assert_error(
        ("appointment_info", "profile", "phone_number"), "string_pattern_mismatch", pattern=r"^\(\d{3}\) \d{3}-\d{4}$"
    )
    assert_error(
        ("appointment_info", "profile", "target_move_date"), "string_pattern_mismatch", pattern=r"^\d{4}-\d{2}-\d{2}$"
    )
    assert_error(("appointment_info", "marketing_source", "code"), "string_too_short", min_length=1)
    assert_error(("appointment_info", "marketing_source", "doorway_application_id"), "string_too_short", min_length=1)
    assert_error(("appointment_info", "preferences", "min_price"), "greater_than_equal", ge=0)
    assert_error(("appointment_info", "preferences", "max_price"), "greater_than_equal", ge=0)
    assert_error(("appointment_info", "preferences", "selected_times"), "too_short", min_length=1)


def test_schedule_tour_mcp_tool_registration():
    """Test MCP tool registration for schedule tour."""
    # Create mock MCP instance
    mock_mcp = MagicMock()
    mock_tool_decorator = MagicMock()
    mock_mcp.tool = mock_tool_decorator

    # Register the tools
    register_tour_tools(mock_mcp)

    # Get the schedule_tour tool registration call
    schedule_tour_call = [call for call in mock_tool_decorator.call_args_list if call[1]["name"] == "schedule_tour"][0]

    # Verify tool registration
    assert schedule_tour_call[1]["name"] == "schedule_tour"
    assert "Schedule a tour for the given property and time slots" in schedule_tour_call[1]["description"]


def test_schedule_tour_endpoint(valid_request):
    """Test the schedule tour HTTP endpoint."""
    response = client.post("/tour/schedule_tour", json=valid_request.model_dump())

    assert response.status_code == 200
    data = response.json()

    assert data["status_code"] == "existing_queued_request"
    assert data["queued_request"]["property_id"] == valid_request.appointment_info.property_id
    assert data["queued_request"]["status"] == "queued"
