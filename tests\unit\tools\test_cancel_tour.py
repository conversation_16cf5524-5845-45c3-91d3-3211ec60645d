"""Unit tests for cancel tour functionality."""

from datetime import datetime, timezone
from unittest.mock import MagicMock, patch, call

import pytest
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.testclient import TestClient

from src.models.cancel_tour import CancelTourResponse, Appointment, Prospect, Profile, Phone
from src.tools.tour import post_cancel_tour, register_tour_tools, router

# Create a test app and include the router
app = FastAPI()
app.include_router(router, prefix="/tour")
client = TestClient(app)


def test_cancel_tour_success():
    """Test successful tour cancellation."""
    appointment_id = 167581
    response = post_cancel_tour(appointment_id)

    assert isinstance(response, CancelTourResponse)
    assert response.status_code == "ok"

    # Verify appointment details
    appointment = response.appointment
    assert appointment.id == appointment_id
    assert appointment.status == "cancelled"
    assert appointment.type == "request"
    assert not appointment.is_deleted
    assert appointment.auto_accepted

    # Verify prospect details
    prospect = appointment.prospect
    assert prospect.status == "cancelled-renter"
    assert prospect.source == "Property Website"
    assert prospect.is_active
    assert not prospect.is_deleted
    assert not prospect.is_excluded
    assert not prospect.is_waitlist

    # Verify profile details
    profile = prospect.profile
    assert profile.first_name == "Adr"
    assert profile.last_name == "Test"
    assert profile.email == "<EMAIL>"
    assert profile.formatted_phone_number == "(*************"
    assert profile.target_move_date == "2024-09-19"
    assert not profile.is_deleted
    assert profile.is_winback_enabled

    # Verify phone details
    phone = profile.phone
    assert phone.caller_id_name == "Invalid Phone"
    assert phone.caller_id_type == "invalid"
    assert phone.can_receive_call
    assert phone.can_receive_sms
    assert phone.country_code == "US"
    assert phone.national_format == "(*************"
    assert phone.phone_number == "+12222222222"
    assert not phone.is_deleted


def test_cancel_tour_error_handling():
    """Test error handling when canceling a tour."""
    # Test with non-existent appointment ID
    non_existent_id = 909999
    with patch("src.tools.tour.post_cancel_tour") as mock_cancel:
        mock_cancel.side_effect = HTTPException(status_code=404, detail="Appointment not found")
        response = client.post(f"/tour/cancel_tour/{non_existent_id}")
        assert response.status_code == 404
        assert "Appointment not found" in response.json()["detail"]


def test_cancel_tour_mcp_tool_registration():
    """Test MCP tool registration for cancel tour."""
    # Create mock MCP instance
    mock_mcp = MagicMock()
    mock_tool_decorator = MagicMock()
    mock_mcp.tool = mock_tool_decorator

    # Register the tools
    register_tour_tools(mock_mcp)

    # Get the cancel_tour tool registration call
    cancel_tour_call = [call for call in mock_tool_decorator.call_args_list if call[1]["name"] == "cancel_tour"][0]

    # Verify tool registration
    assert cancel_tour_call[1]["name"] == "cancel_tour"
    assert "Cancel a scheduled tour appointment" in cancel_tour_call[1]["description"]


def test_cancel_tour_endpoint():
    """Test the cancel tour HTTP endpoint."""
    appointment_id = 167581
    response = client.post(f"/tour/cancel_tour/{appointment_id}")

    assert response.status_code == 200
    data = response.json()

    assert data["status_code"] == "ok"
    assert data["appointment"]["id"] == appointment_id
    assert data["appointment"]["status"] == "cancelled"
    assert data["appointment"]["type"] == "request"

    # Verify prospect data in response
    prospect = data["appointment"]["prospect"]
    assert prospect["status"] == "cancelled-renter"
    assert prospect["source"] == "Property Website"
    assert prospect["profile"]["first_name"] == "Adr"
    assert prospect["profile"]["last_name"] == "Test"
