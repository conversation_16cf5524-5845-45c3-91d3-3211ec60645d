import os
from enum import Enum


class ConsentStatus(Enum):
    """Enum for consent status values used in consent configurations."""

    GRANTED = "granted"
    DECLINED = "declined"
    REVOKED = "revoked"
    NEW = "new"


secret_name = "mcp-knock"


def is_true(name: str) -> bool:
    s = name.upper()
    return s in ["TRUE", "T", "ON", "YES", "Y"]


consent_configs = {
    ConsentStatus.GRANTED.value: {"has_consent": True, "status": "granted", "note": None},
    ConsentStatus.DECLINED.value: {"has_consent": False, "status": "declined", "note": "User declined SMS consent"},
    ConsentStatus.REVOKED.value: {"has_consent": False, "status": "revoked", "note": "User revoked SMS consent"},
    ConsentStatus.NEW.value: {"has_consent": False, "status": "new", "note": "New consent request"},
}
OTEL_ENABLED = is_true(os.getenv("OTEL_ENABLED", ""))
OTEL_ENDPOINT = os.getenv("OTEL_ENDPOINT", "http://localhost:4318")
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()

ENVIRONMENT = os.getenv("ENVIRONMENT", "local")
APP_NAME = os.getenv("APP_NAME", "mcp-knock")
