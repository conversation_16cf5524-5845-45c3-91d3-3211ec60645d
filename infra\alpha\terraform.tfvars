environment       = "alpha"
application       = "mcp-knock"
cluster           = "knock_alpha_main"
tags = {
  "knock:service"           = "mcp-knock"
  "knock:application"       = "mcp-knock"
  "knock:allocation"        = "development"
  "knock:owning-team-email" = "<EMAIL>"
  "knock:managed-via"       = "terraform"
  "knock:repository-link"   = "https://github.com/knockrentals/mcp-knock"
  "knock:critical-infra"    = "no"
}
image_repo_names   = ["mcp-knock"]
github_repo_name   = "mcp-knock-deploy-test"
github_org         = "knockrentals"
vpc_name           = "cc-alpha-vpc"
host_header_values = ["alpha-mcp-knock.knocktest.com"]
container_port = "8000"
container_healthcheck_path = "/api/health"
aws_account = ************
app_image_tag = "placeholder"
