#!/usr/bin/env python3
"""Test different MCP method names to find the correct one."""

import requests

def test_methods():
    s = requests.Session()
    
    # Initialize session
    r1 = s.post('http://localhost:8000/mcp', 
                json={
                    'jsonrpc': '2.0', 
                    'id': 1, 
                    'method': 'initialize', 
                    'params': {
                        'protocolVersion': '2024-11-05',
                        'capabilities': {'tools': {}},
                        'clientInfo': {'name': 'test', 'version': '1.0'}
                    }
                }, 
                headers={'Accept': 'application/json, text/event-stream'})
    
    session_id = r1.headers.get('mcp-session-id')
    print(f"Session ID: {session_id}")
    print(f"Init response: {r1.text}")
    
    # Test tools/list with different parameter structures
    test_cases = [
        ('tools/list', {}),
        ('tools/list', None),
        ('tools/list', {'cursor': None}),
        ('tools/list', {'limit': 100}),
        ('tools/list', {'cursor': None, 'limit': 100}),
    ]

    for method, params in test_cases:
        if params is None:
            payload = {'jsonrpc': '2.0', 'id': 2, 'method': method}
        else:
            payload = {'jsonrpc': '2.0', 'id': 2, 'method': method, 'params': params}

        r2 = s.post('http://localhost:8000/mcp',
                    json=payload,
                    headers={'Accept': 'application/json, text/event-stream', 'mcp-session-id': session_id})
        print(f"{method} (params={params}): {r2.status_code}")
        print(f"Response: {r2.text}")
        print("---")

if __name__ == "__main__":
    test_methods()
