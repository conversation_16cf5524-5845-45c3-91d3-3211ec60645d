from datetime import date, datetime, timedelta
from unittest.mock import MagicMock, patch

import pytest

from src.models.appointment import Appointment, PropertyAvailableTimeslots
from src.tools.appointment import (
    _get_property_available_timeslots,
    _get_prospect_appointments,
    compress_timeslots,
    get_natural_date_label,
    register_appointment_tools,
)
from tests.unit.tools.conftest import DummyMCP


class TestGetNaturalDateLabel:
    def test_today_label(self):
        assert get_natural_date_label(date.today()) == "today"

    def test_today_label_with_datetime(self):
        today_dt = datetime.now()
        assert get_natural_date_label(today_dt) == "today"

    def test_today_label_with_str(self):
        today_str = date.today().strftime("%Y-%m-%d")
        assert get_natural_date_label(today_str) == "today"

    def test_tomorrow_label(self):
        assert get_natural_date_label(date.today() + timedelta(days=1)) == "tomorrow"

    def test_formatted_date_label(self):
        some_day = date(2023, 5, 30)
        assert get_natural_date_label(some_day) == "Tuesday, May 30"

    def test_formatted_label_for_other_date(self):
        some_day = date.today() + timedelta(days=5)
        expected = some_day.strftime("%A, %B %d")
        assert get_natural_date_label(some_day) == expected

    def test_custom_format(self):
        some_day = date.today() + timedelta(days=10)
        fmt = "%Y/%m/%d"
        expected = some_day.strftime(fmt)
        assert get_natural_date_label(some_day, fmt=fmt) == expected

    def test_with_timezone_today(self):
        # Use a timezone string, e.g., 'America/Chicago'
        tz_str = "America/Chicago"
        today = datetime.now().date()
        assert get_natural_date_label(today, tz_str=tz_str) == "today"

    def test_invalid_timezone_fallback(self):
        # Should fallback to local date if timezone is invalid
        today = date.today()
        assert get_natural_date_label(today, tz_str="Invalid/Timezone") == "today"


class TestCompressTimeslots:
    def test_compress_timeslots_basic(self):
        today_str = date.today()
        tomorrow_str = date.today() + timedelta(days=1)
        data = [
            f"{today_str}T09:00:00-06:00",
            f"{today_str}T10:30:00-06:00",
            f"{tomorrow_str}T11:00:00-06:00",
        ]
        result = compress_timeslots(data)
        assert any("today" in k for k in result)
        assert any("tomorrow" in k for k in result)
        assert result[next(k for k in result if "today" in k)]["times"] == "09:00:00, 10:30:00"
        assert result[next(k for k in result if "tomorrow" in k)]["times"] == "11:00:00"

    def test_compress_timeslots_with_date_range(self):
        base_date = date.today()
        d1 = base_date
        d2 = base_date + timedelta(days=1)
        d3 = base_date + timedelta(days=2)
        data = [
            f"{d1}T09:00:00-06:00",
            f"{d2}T10:00:00-06:00",
            f"{d3}T11:00:00-06:00",
        ]
        result = compress_timeslots(data, date_from=d2, date_to=d3)
        assert len(result) == 2

    def test_compress_timeslots_handles_invalid_format(self):
        data = [
            "invalid_string",
            "2025-02-13T19:30:00-06:00",
        ]
        result = compress_timeslots(data)
        assert any("2025" in k for k in result)
        assert all("times" in v for v in result.values())

    def test_compress_timeslots_offset_parsing(self):
        data = [
            "2025-02-13T19:30:00-06:00",
            "2025-02-13T20:00:00+05:00",
            "2025-02-13T21:00:00",
        ]
        result = compress_timeslots(data)
        offsets = [v["offset"] for v in result.values()]
        assert "+05:00" in offsets or "-06:00" in offsets or "+00:00" in offsets

    def test_compress_timeslots_time_formatting_seconds(self):
        data = [
            "2025-02-13T19:30:15-06:00",
            "2025-02-13T19:30:00-06:00",
        ]
        result = compress_timeslots(data)
        times = next(iter(result.values()))["times"]
        assert "19:30:15" in times
        assert "19:30" in times

    def test_compress_timeslots_with_property_timezone(self, monkeypatch):
        # Patch get_natural_date_label to always return "today"
        monkeypatch.setattr("src.tools.appointment.get_natural_date_label", lambda *a, **kw: "today")
        data = ["2025-02-13T19:30:00-06:00"]
        result = compress_timeslots(data, property_timezone="America/Chicago")
        assert any("today" in k for k in result)


class TestGetProspectAppointments:
    def test_get_prospect_appointments_returns_appointment(self):
        property_id = 123
        renter_id = 456
        result = _get_prospect_appointments(property_id, renter_id)
        assert isinstance(result, Appointment)
        assert result.appointment_id == 333
        assert result.appointment_status == "confirmed"
        assert result.appointment_start_time == datetime.fromisoformat("2025-07-20T19:30:00-06:00")


class TestRegisterAppointmentTools:
    def test_register_appointment_tools_creates_tools(self):
        mcp = DummyMCP()
        register_appointment_tools(mcp)
        assert "get_property_available_timeslots" in mcp.tools
        assert "get_prospect_appointments" in mcp.tools

    def test_get_property_available_timeslots_tool_exists(self):
        mcp = DummyMCP()
        register_appointment_tools(mcp)
        assert callable(mcp.tools["get_property_available_timeslots"])

    def test_get_prospect_appointments_tool_exists(self):
        mcp = DummyMCP()
        register_appointment_tools(mcp)
        assert callable(mcp.tools["get_prospect_appointments"])


class TestGetPropertyAvailableTimeslots:
    def test_get_property_available_timeslots_no_property_id(self):
        with pytest.raises(ValueError, match="Property ID cannot be None"):
            _get_property_available_timeslots(
                property_id=None,
                renter_id=2,
                date_from="2025-01-01",
                date_to="2025-01-02",
            )

    def test_get_property_available_timeslots_no_renter_id(self):
        with pytest.raises(ValueError, match="Renter ID cannot be None"):
            _get_property_available_timeslots(
                property_id=1,
                renter_id=None,
                date_from="2025-01-01",
                date_to="2025-01-02",
            )

    def test_get_property_available_timeslots_acceptables(self, monkeypatch):
        # Mock get_available_times to return only acceptable_times
        mock_times = [
            (date.today() + timedelta(days=1)).strftime("%Y-%m-%dT09:00:00-06:00"),
            (date.today() + timedelta(days=1)).strftime("%Y-%m-%dT10:00:00-06:00"),
        ]
        monkeypatch.setattr(
            "src.tools.appointment.get_available_times",
            lambda property_id: {
                "available_times": {
                    "acceptable_times": mock_times,
                    "reviewable_times": [],
                }
            },
        )
        result = _get_property_available_timeslots(
            property_id=1,
            renter_id=2,
            date_from=date.today(),
            date_to=(date.today() + timedelta(days=2)),
            property_timezone=None,
        )
        assert isinstance(result, PropertyAvailableTimeslots)
        assert "self_schedule is ON" in result.self_schedule_note
        assert result.property_available_times
        assert result.searched_date_from
        assert result.searched_date_to

    def test_get_property_available_timeslots_reviewables(self, monkeypatch):
        # Mock get_available_times to return only reviewable_times
        mock_times = [
            (date.today() + timedelta(days=2)).strftime("%Y-%m-%dT11:00:00-06:00"),
        ]
        monkeypatch.setattr(
            "src.tools.appointment.get_available_times",
            lambda property_id: {
                "available_times": {
                    "acceptable_times": [],
                    "reviewable_times": [],
                },
                "reviewable_times": {
                    "reviewable_times": mock_times,
                },
            },
        )
        result = _get_property_available_timeslots(
            property_id=1,
            renter_id=2,
            date_from=date.today(),
            date_to=(date.today() + timedelta(days=3)),
            property_timezone=None,
        )
        assert isinstance(result, PropertyAvailableTimeslots)
        assert "No available times found" in result.self_schedule_note
        assert result.property_available_times == {}
        assert result.searched_date_from is not None
        assert result.searched_date_to is not None

    def test_get_property_available_timeslots_both_types(self, monkeypatch):
        # Both acceptable_times and reviewable_times present, should prefer acceptable_times
        acceptable = [
            (date.today() + timedelta(days=1)).strftime("%Y-%m-%dT09:00:00-06:00"),
        ]
        reviewable = [
            (date.today() + timedelta(days=2)).strftime("%Y-%m-%dT11:00:00-06:00"),
        ]
        monkeypatch.setattr(
            "src.tools.appointment.get_available_times",
            lambda property_id: {
                "available_times": {
                    "acceptable_times": acceptable,
                    "reviewable_times": [],
                },
                "reviewable_times": {
                    "reviewable_times": reviewable,
                },
            },
        )
        result = _get_property_available_timeslots(
            property_id=1,
            renter_id=2,
            date_from=date.today(),
            date_to=(date.today() + timedelta(days=3)),
            property_timezone=None,
        )
        assert isinstance(result, PropertyAvailableTimeslots)
        assert "self_schedule is ON" in result.self_schedule_note
        assert result.property_available_times

    def test_get_property_available_timeslots_no_times(self, monkeypatch):
        monkeypatch.setattr(
            "src.tools.appointment.get_available_times",
            lambda property_id: {
                "available_times": {
                    "acceptable_times": [],
                    "reviewable_times": [],
                }
            },
        )
        result = _get_property_available_timeslots(
            property_id=1,
            renter_id=2,
            date_from="2025-01-01",
            date_to="2025-01-02",
        )
        assert isinstance(result, PropertyAvailableTimeslots)
        assert result.property_available_times == {}
        assert result.self_schedule_note == "No available times found"
