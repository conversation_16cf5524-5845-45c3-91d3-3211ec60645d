from pydantic import BaseModel, HttpUrl


class ScheduleTourLink(BaseModel):
    """
    Model for the schedule tour link.
    """

    url: HttpUrl


class AvailableTimes(BaseModel):
    acceptable_times: list[str]
    allow_non_preferred_times: str
    preferred_times: list[str]
    preferred_times_instant_book: str
    reviewable_times: list[str]


class PropertyAvailableTimesResponse(BaseModel):
    available_times: AvailableTimes
