from datetime import datetime

from pydantic import BaseModel, Field


class Phone(BaseModel):
    caller_id_name: str
    caller_id_type: str
    can_receive_call: bool
    can_receive_sms: bool
    carrier_name: str
    carrier_type: str
    country_code: str
    created_time: datetime
    id: int
    is_deleted: bool
    modified_time: datetime
    national_format: str
    phone_number: str


class Profile(BaseModel):
    address_id: int | None = None
    bio: str | None = None
    co_tenants: str | None = None
    created_by_type: str
    created_time: datetime
    email: str
    first_name: str
    formatted_phone_number: str
    id: int
    id_verified: bool | None = None
    id_verify_report_url: str | None = None
    income: float | None = None
    is_criminal: bool | None = None
    is_deleted: bool
    is_winback_enabled: bool
    last_name: str
    modified_time: datetime
    pets: str | None = None
    phone: Phone
    phone_id: int
    phone_number: str
    photo: str
    selfiescan_pdf_key: str | None = None
    target_move_date: str = Field(..., pattern=r"^\d{4}-\d{2}-\d{2}$")
    verification_method: str | None = None
    was_evicted: bool | None = None


class Prospect(BaseModel):
    ai_email_enabled: bool
    assigned_manager_id: int
    assigned_relay_phone: str
    business_time_to_first_response: str | None = None
    created_time: datetime
    creation_source: str
    developer_id: int | None = None
    first_contact_type: str
    first_response_time: datetime
    has_call_recording: bool | None = None
    has_note: bool
    id: int
    is_active: bool
    is_deleted: bool
    is_excluded: bool
    is_waitlist: bool
    last_contacted_time: datetime
    last_relevant_time: datetime
    last_response_time: datetime | None = None
    leased_date: str | None = None
    modified_time: datetime
    origin: str
    pms_created_time: datetime | None = None
    preferences_id: int
    profile: Profile
    profile_id: int
    property_id: int
    renter_id: int
    resource_id: int
    sms_consent_id: int
    source: str
    status: str
    stream_id: str
    time_to_first_response: str | None = None


class Appointment(BaseModel):
    auto_accepted: bool
    created_time: datetime
    developer_id: int | None = None
    end_time: datetime
    id: int
    is_deleted: bool
    manager_id: int
    modified_time: datetime
    origin: str
    prospect: Prospect
    prospect_id: int
    reference_info: str | None = None
    reminder_id: int | None = None
    request_id: int | None = None
    resource_id: int
    review_reminder_id: int | None = None
    review_token: str | None = None
    source: str
    start_time: datetime
    status: str = Field(..., pattern="^cancelled$")
    stream_id: str
    tour_type: str | None = None
    type: str
    uuid: str


class CancelTourResponse(BaseModel):
    appointment: Appointment
    status_code: str = Field(..., pattern="^ok$")
