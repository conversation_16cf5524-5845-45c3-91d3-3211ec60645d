"""
Main application file for the Knock MCP Server.

This file initializes the FastAPI application, sets up logging,
configures the MCP server, and defines the entry point for running the application.
"""

import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI
from fastmcp import FastMCP

from src.routes.router import router as health_check_router
from src.tools.appointment import register_appointment_tools
from src.tools.pna import register_pna_tools
from src.tools.pna import router as pna_router
from src.tools.property_info import register_property_info_tools
from src.tools.property_info import router as property_info_router
from src.tools.prospect import register_prospect_tools
from src.tools.prospect import router as prospect_router
from src.tools.tour import register_tour_tools
from src.tools.tour import router as tour_router
from src.utils.log_config import get_logger, setup_logging
from src.utils.otel_setup import setup_opentelemetry

# Load environment variables from a .env file
load_dotenv()

mcp = FastMCP(
    name="mcp-knock",
    instructions="An MCP server for the Knock platform",
    on_duplicate_tools="error",
    on_duplicate_resources="warn",
    on_duplicate_prompts="replace",
)

mcp_app = mcp.streamable_http_app(path="/")

app = FastAPI(
    title="Knock MCP Server",
    description="A server for the Model Context Protocol (MCP) system.",
    version="0.0.1",
    lifespan=mcp_app.lifespan,
)

# Set up application-wide logging
setup_logging()

# Get logger for main application
logger = get_logger(__name__)

# Include the MCP app
app.mount("/mcp", mcp_app)
register_appointment_tools(mcp)
register_pna_tools(mcp)
register_prospect_tools(mcp)
register_property_info_tools(mcp)
register_tour_tools(mcp)

# Include the routers
app.include_router(health_check_router, prefix="/api", tags=["Health Check"])
app.include_router(prospect_router, prefix="/prospect", tags=["Prospect Guest Card"])
app.include_router(pna_router, prefix="/pna", tags=["Pricing and Availability"])
app.include_router(property_info_router, prefix="/propertyinfo", tags=["Knock Property Info"])
app.include_router(tour_router, prefix="/tour", tags=["Tour"])

# Set up OpenTelemetry
logger.info("Setting up OpenTelemetry...")
setup_opentelemetry(app)
logger.info("OpenTelemetry setup completed")

# Log the start of the application
logger.info("Starting Knock MCP Server application")


def main() -> None:
    """
    Main entry point for running the application in development mode.

    This function mounts the MCP server to the FastAPI app and starts the Uvicorn server
    with auto-reloading enabled, making it ideal for local development.
    """
    logger.info("Starting Uvicorn server in development mode")
    logger.info("Server will be available at http://0.0.0.0:8000")
    logger.info("Auto-reload is enabled for development")

    # Mount the MCP server to the FastAPI application
    # This exposes the MCP-specific endpoints.
    # Start the Uvicorn server
    # The application will be served on 0.0.0.0:8000, making it accessible
    # from the local network. The `reload=True` flag enables auto-reloading
    # during development, and should not be used in production.
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)


if __name__ == "__main__":
    main()
