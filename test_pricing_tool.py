#!/usr/bin/env python3
"""Test the pricing and availability tool."""

import asyncio
import sys
import os

# Add the examples directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'examples'))

from mcp_cli_client import MCPClient, MCPConfig

async def test_pricing_tool():
    """Test the pricing and availability tool."""
    config = MCPConfig(
        server_url="http://localhost:8000",
        mcp_path="/mcp",
        timeout=30,
        log_level="INFO"
    )
    
    async with MCPClient(config) as client:
        print("Testing pricing and availability tool...")
        
        result = await client.call_tool(
            "pricing_and_availability",
            {
                "property_id": 12345,
                "bedrooms": "2",
                "move_date": "2025-01-15"
            }
        )
        
        print("Result:")
        print(result)

if __name__ == "__main__":
    asyncio.run(test_pricing_tool())
